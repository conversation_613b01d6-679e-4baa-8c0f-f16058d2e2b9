import useCatalogueActionsHook from "../useCatalogueActionsHook/useCatalogueActionsHook";
import { useEffect } from "react";
import { useDastBoard } from "@/store/Auth/Dashboard/Dashboardstore";

const useCatalogueHook = () => {
  const shopId = useDastBoard((state) => state.shopId);
  const {loadDataCatalogueData} = useCatalogueActionsHook();
  const load = async ({keyword = ""}: {keyword?: string}) => {
    await loadDataCatalogueData({
      keyword: keyword,
      shop_id: shopId,
      sort: "ASC",
    });
    console.log("useCatalogueHook completed");
  };
  useEffect(() => {
    load({});
  }, []);
  return {refresh: load};
};

export default useCatalogueHook;
