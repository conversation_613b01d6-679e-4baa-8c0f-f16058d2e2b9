import AddproductField from "../../../../component/AddproductField";
import Checkbox from "expo-checkbox";
import CustomFiled from "../../../../component/CustomFiled";
import CustomFiled2 from "../../../../component/CustomFiled2";
import CustomFiled3 from "../../../../component/CustomFiled3";
import DropDownComponent from "../../../../component/DropDownComponent";
import FileUploder from "../../../../component/FileUploder";
import OnOffButton from "../../../../component/OnOffButton";
import PriceFieldComponent from "../../../../component/PriceFieldComponent";
import React, { useContext, useEffect, useState } from "react";
import RsIcon from "@/assets/icons/₹Icon.svg";
import Slash from "@/assets/icons/Slash.svg";
import SmallPressable, { SmallPressable2 } from "../../../../component/SmallPressable";
import TaxTabs, { OnOffButton2 } from "../../../../component/TaxTabs";
import TextAreaComponent from "../../../../component/TextAreaComponent";
import useGetApiData from "../../../../hooks/useGetApiData";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStackMutate from "@/hook/TenStackMutateHook/useTenStackMutate";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import { MaterialIcons } from "@expo/vector-icons";
import { useRoute } from "@react-navigation/native";
import { Tooltip } from "@rneui/themed";
import { router } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { ActivityIndicator, Alert, Pressable } from "react-native";
import { ScrollView } from "react-native";
import { Dropdown } from "react-native-element-dropdown";
import { useDastBoard } from "../../../../store/Auth/Dashboard/Dashboardstore";
import { useMyShopId } from "../../../../store/index";
import { GlobalPagenumber } from "./_layout";

import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableNativeFeedback,
  TouchableHighlight,
} from "react-native";

const index = () => {
  const [Taxable, setTaxable] = useState(false);
  const {page, setpage} = useContext(GlobalPagenumber);
  const {
    control,
    setValue,
    handleSubmit,
    watch,
    setError,
    formState: {errors},
    clearErrors,
  } = useForm();
  const shopId = useDastBoard((state) => state.shopId);

  const [IsSlash, setIsSlash] = useState(false);
  const [SlashAmount, setSlashAmount] = useState("0");
  const [isUnPackageProduct, setUnPackageProduct] = useState(false);

  const [showProductSuggestion, setProductSuggestion] = useState(false);
  const [SubCategoryList, setSubCategoryList] = useState([]);

  const {data: ProductCategoryData, isLoading: ProductCategoryLoading} = useGetApiData({
    endpoint: "/productCategoryList",
  });

  useEffect(() => {
    setValue("taxable", Taxable);
  }, [Taxable]);

  const formdata = new FormData();
  const {mutate: addProduct, isPending: addProductLoading} = useTenStackMutate({
    invalidateQueriesKey: "product",
    endpoint: "/auth/productAdd",
  });
  useEffect(() => {
    if (!ProductCategoryLoading) {
      let ProductList = ProductCategoryData?.data?.filter((items) => {
        return items.id == watch("category");
      });
      setSubCategoryList(ProductList ? ProductList[0]?.sub_category_list : []);
    }
  }, [watch("category")]);
  const route = useRoute();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setpage(route.name);
  }, []);
  return (
    <Pressable
      onPress={() => {
        setOpen(false);
      }}
    >
      <View className="mt-4">
        <View>
          <Text className="font-[400] text-[18px]">Product Information</Text>
        </View>
        <View className="">
          <AddproductField
            rules={{
              required: {value: true, message: "Please Enter Product Name"},
              validate: (value) => {
                if (value.includes("'") || value.includes('"')) {
                  return "Please remove single and double quotes from the product name";
                }
              },
            }}
            neme={"productname"}
            control={control}
            text={"Product Name"}
            placeholder={"Enter Name of the Product"}
          />
          {errors?.productname?.message ? (
            <View className="relative">
              <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                {errors?.productname?.message}
              </Text>
            </View>
          ) : (
            <></>
          )}
        </View>
        <View>
          <TextAreaComponent
            rules={{
              required: {value: true, message: "Please Enter Description"},
              validate: (value) => {
                if (value.includes("'") || value.includes('"')) {
                  return "Please remove single and double quotes from the product name";
                }
              },
            }}
            text={"Description"}
            placeholder={"Enter Description"}
            name={"description"}
            control={control}
          />
          {errors?.description?.message ? (
            <View className="relative">
              <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                {errors?.description?.message}
              </Text>
            </View>
          ) : (
            <></>
          )}
        </View>
        <View className="mt-4">
          {ProductCategoryLoading ? (
            <></>
          ) : (
            <>
              <View className="mt-2">
                <Text className="font-[400] text-[16px] text-[#4D4D4D]">Product Category</Text>
                <Controller
                  name={"category"}
                  control={control}
                  rules={{
                    required: {value: true, message: "Please Select Category"},
                  }}
                  render={({field: {onChange, value}}) => {
                    return (
                      <>
                        <Dropdown
                          data={ProductCategoryData?.data}
                          placeholder={"Select type of request"}
                          className="border-[1px] border-[#bec9e1] rounded-[4px] px-3 mt-2"
                          containerStyle={{}}
                          placeholderStyle={{
                            color: "#B3B3B3",
                            textAlignVertical: "center",
                          }}
                          style={{
                            height: 40,
                          }}
                          labelField="name"
                          valueField="id"
                          value={value}
                          onChange={(item) => {
                            onChange(item.id);
                          }}
                        />
                      </>
                    );
                  }}
                />
              </View>
            </>
          )}
          {errors?.category?.message ? (
            <View className="relative">
              <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                {errors?.category?.message}
              </Text>
            </View>
          ) : (
            <></>
          )}

          <View className="mt-2">
            <SmallPressable2
              pressfun={() => {
                setProductSuggestion((value) => !value);
              }}
              text={"Add Category suggestion"}
              style={{width: 218}}
              value={showProductSuggestion}
            />
          </View>
        </View>
        <View className="mt-4">
          {ProductCategoryLoading ? (
            <></>
          ) : (
            <>
              {SubCategoryList.length > 0 ? (
                <>
                  <View className="mt-2">
                    <Text className="font-[400] text-[16px] text-[#4D4D4D]">
                      Sub Product Category
                    </Text>
                    <Controller
                      name={"subcategory"}
                      control={control}
                      render={({field: {onChange, value}}) => {
                        return (
                          <>
                            <Dropdown
                              data={SubCategoryList.length > 0 ? SubCategoryList : []}
                              placeholder={"Select type of request"}
                              containerStyle={{}}
                              placeholderStyle={{
                                color: "#B3B3B3",
                                textAlignVertical: "center",
                              }}
                              style={{
                                height: 40,
                              }}
                              labelField="name"
                              valueField="id"
                              value={value}
                              onChange={(item) => {
                                onChange(item.id);
                              }}
                              className="border-[1px] border-[#bec9e1] rounded-[4px] px-3 mt-2"
                            />
                          </>
                        );
                      }}
                    />
                  </View>
                </>
              ) : (
                <></>
              )}
            </>
          )}
        </View>

        {showProductSuggestion ? (
          <>
            <View className="mt-2">
              <AddproductField
                neme={"ProductCategory"}
                control={control}
                text={"Enter Product Category"}
                placeholder={"Enter Product Category"}
              />
            </View>
            <View className="mt-4">
              <TouchableOpacity
                className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                onPress={() => {
                  setProductSuggestion((value) => !value);
                }}
              >
                <Text className="font-[400] text-[#fff] text-[16px]">Submit</Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <></>
        )}

        <View className="mt-2">
          <OnOffButton3
            val={isUnPackageProduct}
            setTaxables={setUnPackageProduct}
            btnText1={"PackageProduct"}
            btnText2={"UnPackageProduct"}
          />
        </View>

        {isUnPackageProduct ? (
          <>
            <CustomFiled3
              text={"Enter Price"}
              name1={"price"}
              name2={"unit"}
              placeholder={"Enter Price!"}
              KeyType={"numeric"}
              data={[
                {label: "kg", value: "1"},
                {label: "lt", value: "2"},
              ]}
              rules1={{
                required: {
                  value: true,
                  message: "Enter Price",
                },
                pattern: {
                  value: /^\d+(\.\d{1,2})?$/,
                  message: "Please enter quantity in format XX.XX (e.g., 25.55)",
                },
              }}
              control={control}
            />
          </>
        ) : (
          <></>
        )}

        {!isUnPackageProduct && (
          <>
            <View className="mt-2">
              <PriceFieldComponent
                rules={{
                  required: {value: true, message: "Please Enter Price"},
                  pattern: {
                    value: /^\d+(\.\d{1,2})?$/,
                    message: "Please enter quantity in format XX.XX (e.g., 25.55)",
                  },
                }}
                neme={"price"}
                control={control}
                text={"Enter price"}
                placeholder={"Enter price"}
                KeyType={"numeric"}
              />
              {errors?.price?.message ? (
                <View className="relative">
                  <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                    {errors?.price?.message}
                  </Text>
                </View>
              ) : (
                <></>
              )}
            </View>
          </>
        )}
        <View className=" my-3 mt-4">
          <View className="">
            <View className="flex-row items-center justify-between">
              <View
                style={{position: "relative"}}
                className="flex-shrink flex-row space-x-1 items-center"
              >
                <Text className="font-[400] text-[16px] leading-[20px]">Select Delivery Mode</Text>
                <View className="relative">
                  {open ? (
                    <>
                      <View className="absolute p-4 border-[1px] border-[#ccc] bg-black rounded-[10px] bottom-[13px]">
                        <Text className="text-white max-h-[200px] max-w-[200px] mb-1">
                          <Text className="font-[600]">Quick Delivery</Text>: Products that can be
                          prepared within 2 minutes by the seller.
                        </Text>
                        <Text className="text-white max-h-[200px] max-w-[200px] mb-1">
                          <Text className="font-[600]">Normal delivery</Text> : Products that can be
                          prepared within an hour by the seller.
                        </Text>
                        <Text className="text-white max-h-[200px] max-w-[200px]">
                          <Text className="font-[600]">Heavy vehicle delivery</Text>: Products that
                          require heave vehicle eg. - truck, etc. to fulfill the delivery.
                        </Text>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}
                </View>
                <TouchableOpacity
                  onPress={() => {
                    setOpen(!open);
                  }}
                  className="w-[20px] h-[20px] rounded-full items-center justify-center bg-black"
                >
                  <Text className="text-[#fff]">?</Text>
                </TouchableOpacity>
              </View>

              <Controller
                name="mode"
                control={control}
                defaultValue={"Normal"}
                render={({field: {onChange, value}}) => {
                  return (
                    <Dropdown
                      style={{
                        minWidth: 100,
                        minHeight: 40,
                        borderColor: "#ACB9D5",
                        borderWidth: 1,
                        paddingLeft: 10,
                        borderRadius: 4,
                      }}
                      placeholder={"Options"}
                      placeholderStyle={{
                        color: "#B3B3B3",
                        height: 35,
                        textAlignVertical: "center",
                      }}
                      data={[
                        {
                          label: "Quick",
                          value: "Quick",
                        },
                        {
                          label: "Normal",
                          value: "Normal",
                        },
                        {
                          label: "Heavy",
                          value: "Heavy",
                        },
                      ]}
                      labelField="label"
                      valueField="value"
                      value={value}
                      onChange={(item) => {
                        onChange(item.value);
                      }}
                    />
                  );
                }}
              />
            </View>
          </View>
        </View>

        <View className="">
          <View className="">
            <DropDownComponent
              control={control}
              name={"warranty/guaranty"}
              data={[
                {label: "warranty", label: "warranty"},
                {label: "guaranty", label: "guaranty"},
                {label: "none", label: "none"},
              ]}
              setvaluefun={setValue}
              defaul={"none"}
              text={"Does the product has warranty/guaranty?(optional)"}
            />
          </View>
          {watch("warranty/guaranty") != "" &&
          watch("warranty/guaranty") != undefined &&
          watch("warranty/guaranty") != "none" ? (
            <>
              <View>
                <CustomFiled
                  text={"What is the time duration for a warranty or guarantee ?"}
                  placeholder={"Enter time"}
                  firstfieldname={"warOrguarName"}
                  secoundfieldname={"dur"}
                  unitsplace={"Select"}
                  data={[
                    {label: "Month", value: "1"},
                    {label: "Year", value: "2"},
                  ]}
                  control={control}
                />
              </View>
            </>
          ) : (
            <></>
          )}
        </View>

        {!isUnPackageProduct && (
          <>
            <CustomFiled3
              text={"Enter qty"}
              name1={"qty"}
              name2={"unit"}
              placeholder={"Enter qty!"}
              KeyType={"numeric"}
              rules1={{
                required: {value: true, message: "Please Enter qty"},
                pattern: {
                  value: /^\d+(\.\d{1,2})?$/,
                  message: "Please enter quantity in format XX.XX (e.g., 25.55)",
                },
              }}
              rules2={{
                required: {value: true, message: "Please Enter qty"},
              }}
              data={[
                {label: "Kg", value: "1"},
                {label: "ltr", value: "2"},
                {label: "pcs", value: "3"},
              ]}
              control={control}
            />
            <View className="flex-row space-x-4 justify-between">
              {errors?.qty?.message ? (
                <View className="relative">
                  <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                    {errors?.qty?.message}
                  </Text>
                </View>
              ) : (
                <></>
              )}
              {errors?.unit?.message ? (
                <View className="relative">
                  <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                    {errors?.unit?.message}
                  </Text>
                </View>
              ) : (
                <></>
              )}
            </View>
          </>
        )}

        <View className="flex-row items-center justify-between my-3 mt-4">
          <View className="flex-shrink">
            <Text className="font-[400] text-[16px] leading-[20px]">
              Want to show some discount?
            </Text>
          </View>
          <View className="">
            <OnOffButton setTaxables={setIsSlash} />
          </View>
        </View>
        {IsSlash ? (
          <>
            <View className="flex-row items-center border-[1px] border-[#627164] justify-center p-4 mt-4 w-full space-x-5">
              <View className="flex-1">
                <Text className="font-[400] text-[16px] leading-[20px] text-[#4D4D4D]">
                  Discounted Amount
                </Text>
                <View className="flex-1 pl-2 rounded-[4px] flex-row items-center border-[1px] border-[#ACB9D5] h-[40px] mt-2">
                  <View>
                    <RsIcon />
                  </View>
                  <View className="flex-1">
                    <Controller
                      name="slashedamount"
                      control={control}
                      rules={{
                        required: {value: true, message: "Please Enter discounted amount"},
                        pattern: {
                          value: /^\d+(\.\d{1,2})?$/,
                          message: "Please enter quantity in format XX.XX (e.g., 25.55)",
                        },
                      }}
                      defaultValue={SlashAmount}
                      render={({field: {onChange, value}}) => {
                        return (
                          <>
                            <TextInput
                              className="pl-2 font-[500] text-[16px] leading-[24px] flex-1"
                              value={value}
                              onChangeText={onChange}
                              editable={IsSlash}
                              keyboardType="numeric"
                            />
                          </>
                        );
                      }}
                    />
                  </View>
                </View>
              </View>
            </View>
            {errors?.actualamount?.message ? (
              <View className="relative">
                <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                  {errors?.actualamount?.message}
                </Text>
              </View>
            ) : (
              <></>
            )}
          </>
        ) : (
          <></>
        )}

        <View className="mt-4">
          <View className="flex-row items-center justify-between my-2">
            <Text>Is the Product Taxable?</Text>
            <View className="">
              <OnOffButton setTaxables={setTaxable} />
            </View>
          </View>
        </View>
        {Taxable ? (
          <>
            <TaxTabs Taxable={Taxable} control={control} />
          </>
        ) : (
          <></>
        )}
        <View className="">
          <FileUploder title={"Upload an Image"} text={"Browse Files"} setValue={setValue} />
        </View>
        {errors?.productimage?.message ? (
          <View className="relative">
            <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
              {errors?.productimage?.message}
            </Text>
          </View>
        ) : (
          <></>
        )}
        <View className="mt-5">
          <TouchableOpacity
            className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
            onPress={() => {
              console.log(
                `duration${Number(watch("dur"))} mon oy yser ${Number(watch("warOrguarName"))} `,
              );
              const patten = /^\d+(\.\d{1,2})?$/;
              console.log(Number(IsSlash));
              console.log(watch("slashedamount"));
              try {
                if (watch("productimage") === null || watch("productimage") === undefined) {
                  setError("productimage", {message: "Please Upload Image"});
                } else {
                  clearErrors("productimage");
                }
                if (Taxable) {
                  if (watch("CGST") === "" || watch("CGST") === undefined) {
                    alert("Please Enter CGST");
                    return;
                  }
                  if (watch("SGST") === "" || watch("SGST") === undefined) {
                    alert("Please Enter SGST");
                    return;
                  }
                  if (watch("CESS") === "" || watch("CESS") === undefined) {
                    alert("Please Enter CESS");
                    return;
                  }
                  if (!patten.test(watch("CESS"))) {
                    alert("Please enter quantity in format XX.XX (e.g., 25.55)");
                    return;
                  }
                  if (!patten.test(watch("CGST"))) {
                    alert("Please enter quantity in format XX.XX (e.g., 25.55)");
                    return;
                  }
                  if (!patten.test(watch("SGST"))) {
                    alert("Please enter quantity in format XX.XX (e.g., 25.55)");
                    return;
                  }
                }
                handleSubmit(async (datas) => {
                  formdata.append("shop_id", shopId.toString());
                  formdata.append("product_category_id", datas?.category ? datas?.category : 1);
                  formdata.append("name", datas?.productname ? datas?.productname : "Product");
                  formdata.append("description", datas?.description ? datas?.description : "");
                  formdata.append("price", datas?.price ? datas?.price : "0");
                  formdata.append("is_discounted_prd", Number(IsSlash).toString());
                  formdata.append("slash_price", IsSlash ? datas?.slashedamount : 0);
                  formdata.append("qty", isUnPackageProduct ? 0 : datas?.qty);
                  formdata.append("unit", datas?.unit ? datas?.unit : "Kg");
                  formdata.append("is_taxable", Number(Taxable).toString());
                  formdata.append("cgst", Taxable ? datas?.CGST : 0);
                  formdata.append("sgst", Taxable ? datas?.SGST : 0);
                  formdata.append("cess", Taxable ? datas?.CESS : 0);
                  formdata.append("image", {
                    uri: datas?.productimage,
                    name: datas?.productimage,
                    filename: datas?.productimage,
                    type: datas?.type ? datas?.type : "image/jpeg",
                  } as any);
                  formdata.append("is_unpackage_prd", Number(isUnPackageProduct).toString());
                  formdata.append("unpackage_prd_price", isUnPackageProduct ? datas?.price : 0);
                  formdata.append("unpackage_prd_unit", isUnPackageProduct ? datas?.unit : "Kg");
                  formdata.append(
                    "delivery_mode",
                    (watch("mode") === "Normal" ? 1 : watch("mode") === "Heavy" ? 2 : 3).toString(),
                  );
                  formdata.append(
                    "prd_cover_by",
                    (watch("warranty/guaranty") === "guaranty"
                      ? 3
                      : watch("warranty/guaranty") === "warranty"
                        ? 2
                        : 1
                    ).toString(),
                  );
                  formdata.append(
                    "prd_cover_num",
                    (watch("warranty/guaranty") === "guaranty" ||
                    watch("warranty/guaranty") === "warranty"
                      ? Number(watch("dur"))
                      : 0
                    ).toString(),
                  );
                  formdata.append(
                    "prd_cover_duration",
                    (watch("warranty/guaranty") === "guaranty" ||
                    watch("warranty/guaranty") === "warranty"
                      ? Number(watch("warOrguarName"))
                      : 0
                    ).toString(),
                  );
                  await addProduct(formdata, {
                    onSuccess: (data: {msg: string; prd_id: number; status: number}) => {
                      setpage(page + 1);
                      console.log(JSON.stringify(formdata, null, 2));
                      console.log(data?.prd_id, "product id");
                      if (data?.status === 1) {
                        router.push({
                          pathname: "/home/<USER>/product/page1",
                          params: {
                            shop_id: shopId,
                            prd_id: data?.prd_id,
                          },
                        });
                      } else {
                        Alert.alert("Error", data?.msg);
                      }
                    },
                    onError: (error) => {
                      console.log(error);
                      Alert.alert("Error", "An error occurred while submitting the form.");
                    },
                  });
                })();
              } catch (error) {
                console.log(error);
                Alert.alert("Error", "An error occurred while submitting the form.");
              }
            }}
          >
            {addProductLoading ? (
              <>
                <ActivityIndicator />
              </>
            ) : (
              <Text className="font-[400] text-[#fff] text-[16px]">Next</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Pressable>
  );
};

export const OnOffButton3 = (props) => {
  const [isOn, setOn] = useState(props?.val ? props?.val : false);

  useEffect(() => {
    setOn(props?.val);
  }, [props?.val]);

  return (
    <View className="">
      <View className="relative flex-row w-[256] items-center justify-between border-[1px] border-[#ACB9D5] rounded-[20px] px-7 py-2">
        <View className="flex-row items-center space-x-4">
          <TouchableOpacity
            onPress={() => {
              setOn(!isOn);
              if (props?.setTaxables) {
                props?.setTaxables((state) => {
                  return !state;
                });
              }
            }}
            className="z-50"
          >
            <Text
              className="font-[400] text-[12px]"
              style={{
                color: isOn ? "#ACB9D5" : "#fff",
              }}
            >
              {props?.btnText1}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setOn(!isOn);
              if (props?.setTaxables) {
                props?.setTaxables((state) => {
                  return !state;
                });
              }
            }}
            className="z-50"
          >
            <Text
              className="font-[400] text-[12px]"
              style={{
                color: isOn ? "#fff" : "#ACB9D5",
              }}
            >
              {props?.btnText2}
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          onPress={() => {
            setOn(!isOn);
            if (props?.setTaxables) {
              props?.setTaxables((state) => {
                return !state;
              });
            }
          }}
          style={{
            left: isOn ? 120 : 5,
          }}
          className="absolute bg-[#00660A] w-[125px] h-[25px] rounded-[20px]"
        />
      </View>
    </View>
  );
};

export default index;
