import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { ParamType } from "@/core/types/catalogue/catalogue_type";
import { AppDispacher } from "@/redux/store";
import { useDastBoard } from "@/store/Auth/Dashboard/Dashboardstore";

import {
  canShowSaveBtn,
  removeAll,
  setAllCancleQuickEdit,
  setQuickEdit,
} from "@/redux/Catalogue/CatalogueSlice";

import {
  changeProductStockStatus,
  loadData,
  productUpdateToBestSeller,
  productUpdateToFeatured,
  removeProduct,
} from "@/redux/Catalogue/asyncReducers";

const useCatalogueActionsHook = () => {
  const shopId = useDastBoard((state) => state.shopId);
  const dispatch: AppDispacher = useDispatch();

  const updateOutOfStock = async (productid: number) => {
    console.log("hook StockStatus  isLoading");
    await dispatch(
      changeProductStockStatus({
        shop_id: shopId,
        product_id: productid,
        stock_status: 1,
      }),
    );
    console.log("hook StockStatus completes");
  };
  const updateToBestSeller = (productid: number) => {
    dispatch(
      productUpdateToBestSeller({
        shop_id: shopId,
        product_id: productid,
        is_bestseller: 1,
      }),
    );
  };
  const updateToFeatured = (productid: number) => {
    dispatch(
      productUpdateToFeatured({
        shop_id: shopId,
        product_id: productid,
        is_featured: 1,
      }),
    );
  };

  const removeproducts = (productid?: number) => {
    dispatch(removeProduct({product_id: productid}));
  };
  const removeAllfromList = () => {
    dispatch(removeAll());
  };

  const canShowSaveButton = () => {
    dispatch(canShowSaveBtn());
  };
  const QuickEdit = (productid: number, isEditing: boolean) => {
    dispatch(setQuickEdit({productID: productid, isEditing: isEditing}));
    canShowSaveButton();
  };

  const CancleAllQucikEdit = () => {
    dispatch(setAllCancleQuickEdit());
  };

  const loadDataCatalogueData = useCallback(
    ({...param}: ParamType) => {
      dispatch(
        loadData({
          ...param,
        }),
      );
    },
    [dispatch],
  );

  return {
    updateOutOfStock,
    updateToBestSeller,
    updateToFeatured,
    removeproducts,
    removeAllfromList,
    QuickEdit,
    canShowSaveButton,
    CancleAllQucikEdit,
    loadDataCatalogueData,
  };
};

export default useCatalogueActionsHook;
