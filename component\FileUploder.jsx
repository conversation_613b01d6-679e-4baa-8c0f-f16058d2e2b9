import * as ImagePicker from "expo-image-picker";
import CamaraIcon from "@/assets/icons/bottemSheetIcon/camara.svg";
import Close from "@/assets/icons/bottemSheetIcon/Close.svg";
import DocIcon from "@/assets/icons/bottemSheetIcon/gallery.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { useRef, useState } from "react";
import UploadIcon from "@/assets/icons/uploadIcon.svg";
import { router } from "expo-router";
import { Image, Text, TouchableOpacity, View } from "react-native";

const FileUploder = ({title, text, setValue, images}) => {
  const [image, setImage] = useState(images ? images : null);
  const refRBSheet = useRef();

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      refRBSheet.current.close();
      setValue("productimage", result.assets[0].uri);
      setValue("type", result.assets[0].mimeType);

      setImage((state) => {
        return result.assets[0].uri;
      });
    }
  };

  const takePicture = async () => {
    let result = await ImagePicker.launchCameraAsync({
      quality: 1,
    });
    if (!result.canceled) {
      setValue("productimage", result.assets[0].uri);
      setValue("type", result.assets[0].mimeType);
      setImage((state) => {
        return result.assets[0].uri;
      });
    }
  };

  return (
    <View className="mt-2">
      <Text>{title}</Text>
      <View className="mt-2">
        <TouchableOpacity
          onPress={() => {
            refRBSheet.current.open();
          }}
          style={{
            width: 154,
          }}
          className="flex-row space-x-2 h-[40] items-center justify-center bg-[#A4F4AC] rounded-[5px]"
        >
          <UploadIcon />
          <Text className="font-[400] text-[16px] text-[#00660A]">{text}</Text>
        </TouchableOpacity>

        <RBSheet
          ref={refRBSheet}
          customStyles={{
            container: {
              borderRadius: 20,
            },
            draggableIcon: {
              backgroundColor: "#000",
            },
          }}
          customModalProps={{
            statusBarTranslucent: true,
          }}
          customAvoidingViewProps={{
            enabled: false,
          }}
        >
          <View className="px-6 py-2 justify-center">
            <View className="mt-2">
              <View className="flex-row justify-between">
                <View />
                <TouchableOpacity
                  onPress={() => {
                    refRBSheet.current.close();
                  }}
                >
                  <Close />
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View className="flex-1 flex-row items-center justify-evenly">
            <TouchableOpacity
              onPress={() => {
                takePicture();
              }}
            >
              <View className="items-center justify-center space-y-2">
                <CamaraIcon width={40} height={40} />
                <Text>Camara</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                pickImage();
              }}
            >
              <View className="items-center justify-center space-y-2">
                <DocIcon width={40} height={40} />
                <Text>Document</Text>
              </View>
            </TouchableOpacity>
          </View>
        </RBSheet>
      </View>
      <View className="flex-row space-x-4 mt-2">
        <View className="rounded-[5px] overflow-hidden">
          <TouchableOpacity
            onPress={() => {
              setImage((state) => {
                return "";
              });
            }}
            className="absolute z-50 right-1 top-1"
          >
            <Close height={10} width={10} />
          </TouchableOpacity>
          {image !== "" && image !== null && (
            <>
              <Image
                source={{
                  uri: image,
                }}
                style={{
                  width: 50,
                  height: 50,
                  objectFit: "fill",
                }}
              />
            </>
          )}
        </View>
      </View>
    </View>
  );
};

export default FileUploder;
