import BackArrow from "../../../assets/icons/BackArrow.svg";
import ColumnComponent from "../../../component/ColumnComponent";
import LoadingScreen from "../../../component/LoadingScreen";
import OrderTable from "../../../component/OrderTable";
import React, { useEffect, useState } from "react";
import RsIcon from "../../../assets/icons/₹Icon.svg";
import UserRatingComponent from "../../../component/UserRatingComponent";
import useTenStack from "@/hook/TenStackHook/useTenStack";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import { router, useLocalSearchParams } from "expo-router";
import { DeviceEventEmitter, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDastBoard } from "@/store/Auth/Dashboard/Dashboardstore";

const index = () => {
  const {id, actions, orderId} = useLocalSearchParams();

  const [Textcolor, setTextcolor] = useState("");
  const [bgcolor, setbgcolor] = useState("");
  const [action, setaction] = useState("");
  const {getOrderStatus} = useDastBoard((state) => state);

  const {data: OrderDetails, isFetching: OrderDetailsLoading} = useTenStack<
    any,
    {order_id: string | string[]}
  >({
    endpoint: "/auth/order_details",
    pram: {order_id: id},
    key: "order_details",
    id: [id.toString()],
    refetch: true,
  });

  const {mutate: SetFunction} = useTenStackMutateD({
    endpoint: "auth/order_status_change",
    invalidateQueriesKey: ["order_details"],
  });

  useEffect(() => {
    setaction(getOrderStatus(Number(actions)));
  }, [actions, getOrderStatus]);

  useEffect(() => {
    if (action === "Accepted" || action === "Ready" || action === "Replacement") {
      setTextcolor("#00660A");
    } else if (action === "Rejected" || actions === "Canceled") {
      setTextcolor("#D13434");
    } else if (action === "Pending") {
      setTextcolor("#D97706");
    } else if (action === "Delivered" || actions === "Return" || actions === "Refund") {
      setTextcolor("#000");
    }
    if (action === "Accepted" || action === "Ready" || action === "Replacement") {
      setbgcolor("#99FFA3");
    } else if (action === "Rejected" || action === "Canceled") {
      setbgcolor("#FFE9E9");
    } else if (action === "Pending") {
      setbgcolor("#FEF3C7");
    } else if (action === "Delivered" || action === "Refund" || action === "Return") {
      setbgcolor("#fff");
    }
  }, [action]);

  const TotalTime = 30;
  const [min, setmin] = useState(TotalTime);
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (action === "Pending") {
      intervalId = setInterval(() => {
        setmin((min) => {
          if (min > 0) {
            return min - 1;
          } else {
            setaction("Rejected");
            SetFunction({
              order_id: id,
              status_id: 3,
            });
            return 0;
          }
        });
      }, 1000);
    }

    // Cleanup function to clear interval when action changes or component unmounts
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [SetFunction, action, id]);
  return (
    <>
      {OrderDetailsLoading ? (
        <>
          <LoadingScreen />
        </>
      ) : (
        <SafeAreaView className="justify-between bg-[#FFFFFF] pb-4 flex-1">
          <ScrollView className="">
            <View className="justify-center">
              {/* Title Area */}
              <View className="flex-row relative items-center justify-start mt-9">
                <TouchableOpacity
                  onPress={() => {
                    router.back();
                  }}
                  className="ml-5 absolute"
                >
                  <View>
                    <BackArrow />
                  </View>
                </TouchableOpacity>
                <View className="flex-1 items-center justify-center">
                  <Text className="font-[400] text-[26px] leading-[39px]">Order Details</Text>
                </View>
              </View>

              {/* Id and Status Area */}
              <View className="flex-row justify-between items-center">
                <View className="items-center justify-center mt-6 flex-1">
                  <View className="mb-4 flex-row items-center gap-2">
                    <Text className="font-[400] text-[16px] leading-[24px]">Order ID:</Text>
                    <Text className="font-[600] text-[16px] leading-[24px] text-[#00660A]">
                      #{orderId}
                    </Text>
                  </View>
                  <View className="flex-row items-center gap-2">
                    <Text className="font-[400] text-[16px] leading-[24px]">Status:</Text>
                    <View className="">
                      <Text
                        style={{
                          backgroundColor: bgcolor,
                          color: Textcolor,
                          borderRadius: 24,
                        }}
                        className="font-[500] text-[12px] text-center px-[8px] py-[4px] leading-[16px] text-[#00660A]"
                      >
                        {action}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
              {action === "Pending" ? (
                <>
                  <View className="px-6 items-center justify-center mt-4">
                    <Text className="">You have {min}sec to accept/reject the order</Text>
                    <View className="bg-[#D9D9D9] w-full h-[8px] my-4 rounded-[10px]">
                      <View
                        style={{
                          width: `${((TotalTime - min) / TotalTime) * 100}%`,
                        }}
                        className="h-full bg-[#00660A] rounded-[10px]"
                      />
                    </View>
                  </View>
                </>
              ) : (
                <></>
              )}

              <View className="mt-6">
                {/* Table Head Area */}
                <View className="flex-row items-center">
                  <View className="flex-1 items-center justify-center mr-3">
                    <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                      No.
                    </Text>
                  </View>
                  <View className="flex-1 items-center justify-center">
                    <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                      Item Name
                    </Text>
                  </View>
                  <View className="flex-1 items-center justify-center">
                    <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                      Rate
                    </Text>
                  </View>
                  <View className="flex-1 items-center justify-center">
                    <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                      Qty
                    </Text>
                  </View>
                  <View className="flex-1 items-center justify-center">
                    <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                      Amount
                    </Text>
                  </View>
                </View>

                {OrderDetailsLoading ? (
                  <></>
                ) : (
                  <>
                    {/* Table Data Area */}
                    {OrderDetails?.order_details?.order_products?.map(
                      ({id, product_name, base_total, quantity, total}: any, index: number) => {
                        return (
                          <OrderTable
                            key={index}
                            text1={`${index + 1}`}
                            text2={product_name}
                            text3={base_total}
                            text4={quantity}
                            text5={total}
                          />
                        );
                      },
                    )}
                  </>
                )}

                {/* Total Amount Area */}
                <View>
                  <View className="flex-row items-center mt-24">
                    <ColumnComponent
                      text={"Items Total"}
                      style={"font-[400] text-[14px] leading-[21px] text-[#627164]"}
                      containerstyle={{
                        alignItems: "flex-start",
                        marginLeft: 40,
                      }}
                    />
                    {OrderDetailsLoading ? (
                      <></>
                    ) : (
                      <>
                        <ColumnComponent
                          text={OrderDetails?.order_details?.total_amnt}
                          style={"font-[500] text-[14px] leading-[21px] text-[#001A03]"}
                          icon={<RsIcon />}
                          containerstyle={{
                            alignItems: "flex-end",
                            marginRight: 20,
                          }}
                        />
                      </>
                    )}
                  </View>
                  <View className="flex-row items-center mt-4">
                    <ColumnComponent
                      text={"GST Charges"}
                      style={"font-[400] text-[14px] leading-[21px] text-[#627164]"}
                      containerstyle={{
                        alignItems: "flex-start",
                        marginLeft: 40,
                      }}
                    />
                    {OrderDetailsLoading ? (
                      <></>
                    ) : (
                      <>
                        <ColumnComponent
                          text={OrderDetails?.order_details?.gst_amnt}
                          style={"font-[500] text-[14px] leading-[21px] text-[#001A03]"}
                          icon={<RsIcon />}
                          containerstyle={{
                            alignItems: "flex-end",
                            marginRight: 20,
                          }}
                        />
                      </>
                    )}
                  </View>

                  <View className="flex-row items-center mt-10">
                    <ColumnComponent
                      text={"Total Receivable"}
                      style={"font-[500] text-[16px] leading-[24px] text-[#627164]"}
                      containerstyle={{
                        alignItems: "flex-start",
                        marginLeft: 40,
                      }}
                    />
                    {OrderDetailsLoading ? (
                      <></>
                    ) : (
                      <>
                        <ColumnComponent
                          text={OrderDetails?.order_details?.total_receive_amnt}
                          style={"font-[700] text-[16px] leading-[24px] text-[#001A03]"}
                          icon={<RsIcon />}
                          containerstyle={{
                            alignItems: "flex-end",
                            marginRight: 20,
                          }}
                        />
                      </>
                    )}
                  </View>
                </View>
              </View>
            </View>
            {/* Button Area */}
            {action === "Rejected" ||
            action === "Delivered" ||
            actions === "Refund" ||
            actions === "Return" ||
            actions === "Canceled" ? (
              <></>
            ) : (
              <>
                <View className="px-5 mt-6">
                  <Text className="font-[600] text-[16px] text-[#627164] leading-[21px]">
                    Ordered by
                  </Text>
                  {OrderDetailsLoading ? (
                    <></>
                  ) : (
                    <>
                      <UserRatingComponent
                        rating={OrderDetails?.order_details?.shop_rating}
                        userData={OrderDetails?.order_details?.customer_details}
                      />
                    </>
                  )}
                </View>
                {action === "Accepted" || actions === "Replacement" ? (
                  <>
                    <View className="mt-10 px-4 flex-row items-center justify-center">
                      <TouchableOpacity
                        onPress={async () => {
                          await SetFunction({
                            order_id: id,
                            status_id: 4,
                          });
                          DeviceEventEmitter.emit("orderStatusChanged");
                          router.push("/home/<USER>/order");
                        }}
                        className="w-full h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                      >
                        <Text className="text-[#fff] font-[600] text-[16px] leading-[24px]">
                          Ready For Deliver
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                ) : (
                  <>
                    {action === "Ready" ? (
                      <>
                        <View className="mt-10 px-4 flex-row items-center justify-center">
                          <TouchableOpacity
                            disabled={true}
                            onPress={async () => {
                              await SetFunction({
                                order_id: id,
                                status_id: 5,
                              });
                              DeviceEventEmitter.emit("orderStatusChanged");
                              setaction("Delivered");
                            }}
                            className="w-full h-[44px] items-center justify-center rounded-[4px] border-[1.5px] border-[#00660A]"
                          >
                            <Text className="text-[#00660A] font-[600] text-[16px] leading-[24px]">
                              Ready For Deliver
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </>
                    ) : (
                      <>
                        <View className="flex-row items-center justify-center mb-6 gap-[8px] mt-4">
                          <TouchableOpacity
                            onPress={async () => {
                              await SetFunction({
                                order_id: id,
                                status_id: 3,
                              });
                              DeviceEventEmitter.emit("orderStatusChanged");
                              setaction("Rejected");
                            }}
                            className="w-[162px] items-center justify-center h-[44px] rounded-[4px] border-[1.5px] border-[#00660A]"
                          >
                            <Text className="text-[#00660A] font-[400] text-[16px] leading-[24px]">
                              Reject
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            className="w-[162px] h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                            onPress={async () => {
                              await SetFunction({
                                order_id: id,
                                status_id: 2,
                              });
                              DeviceEventEmitter.emit("orderStatusChanged");
                              setaction("Accepted");
                            }}
                          >
                            <Text className="text-[#fff] font-[400] text-[16px] leading-[24px]">
                              Accept
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </>
                    )}
                  </>
                )}
              </>
            )}
          </ScrollView>
        </SafeAreaView>
      )}
    </>
  );
};

export default index;
