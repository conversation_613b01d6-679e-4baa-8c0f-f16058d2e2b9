import Addcategory from "../../../assets/icons/addcategory.svg";
import Animated, { FadeInUp, LinearTransition } from "react-native-reanimated";
import Asc from "../../../assets/icons/cat/ascending.svg";
import CatalogueTableComponent from "@/components/CatalogueTableComponent/CatalogueTableComponent";
import Checkbox from "expo-checkbox";
import Defaul from "../../../assets/icons/cat/default.svg";
import Des from "../../../assets/icons/cat/descending.svg";
import PdfIcon from "../../../assets/icons/pdfIcon.svg";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import SearchIcon from "../../../assets/icons/SearchIcon.svg";
import SlideTabs from "../../../component/SlideTabs";
import UploadIcon from "../../../assets/icons/uploadIcon.svg";
import useCatalogueActionsHook from "@/hook/useCatalogueActionsHook/useCatalogueActionsHook";
import useCatalogueHook from "@/hook/useCatalogueHook/useCatalogueHook";
import useTenStack from "@/hook/TenStackHook/useTenStack";
import { router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import { AppDispacher, Rootstate } from "../../../redux/store";
import { useCatalogucategory } from "../../../store";
import { CatalogueModel } from "@/data/model/inedx";
import { removeAll, selectAll } from "@/redux/Catalogue/CatalogueSlice";
import { selectIsLoading, selectIsSelectAll, selectProductData } from "@/redux/Catalogue/meno";

import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";

const Catalogue = () => {
  const {FilterTab} = useCatalogucategory((state) => state);
  const [ActiveFilterTab, setActiveFilterTab] = useState("All");

  const [priceSort, setPriceSort] = useState<"DESC" | "ASC" | "">("");
  const [qtySort, setQtySort] = useState<"DESC" | "ASC" | "">("");

  const [SearchVal, setSearchVal] = useState("");
  const {canShowSaveButton} = useCatalogueActionsHook();
  const {refresh} = useCatalogueHook();
  const productData = useSelector(selectProductData);
  const isSelectAll = useSelector(selectIsSelectAll);
  const isLoading = useSelector(selectIsLoading);

  const dispacher = useDispatch<AppDispacher>();

  const serachDebounce = useMemo(() => {
    let timer: NodeJS.Timeout;
    return (callback: () => void, delay: number) => {
      clearTimeout(timer);
      timer = setTimeout(callback, delay);
    };
  }, []);

  useEffect(() => {
    serachDebounce(() => {
      refresh({keyword: SearchVal});
    }, 1000);
  }, [SearchVal]);

  const handleSelectAll = useCallback(() => {
    if (isSelectAll) {
      dispacher(removeAll());
    } else {
      dispacher(selectAll());
    }
  }, [dispacher, isSelectAll]);
  const renderItem = useCallback(
    ({item}: {item: CatalogueModel}) => {
      const {
        id,
        image,
        isSelected,
        is_best_seller,
        is_featured,
        name,
        price,
        qty,
        isValuesEditing,
        stock_status,
        ...props
      } = item;
      if (ActiveFilterTab !== "All" && ActiveFilterTab === "Out of Stock" && !stock_status) {
        return null;
      }
      if (ActiveFilterTab !== "All" && ActiveFilterTab === "Best Selling" && !is_best_seller) {
        return null;
      }
      if (ActiveFilterTab !== "All" && ActiveFilterTab === "Featured" && !is_featured) {
        return null;
      }
      return (
        <CatalogueTableComponent
          name={name}
          image={{uri: image}}
          qtys={qty}
          isValuesEditing={isValuesEditing}
          price={price}
          id={id}
          isselect={isSelected}
          isOutofStock={stock_status}
          isBestSelling={is_best_seller}
          refresh={(keyword) => refresh({keyword})}
          isFeaturedProduct={is_featured}
          isLoading={isLoading}
          {...props}
        />
      );
    },
    [ActiveFilterTab, isLoading, refresh],
  );
  const listHeaderComponent = () => {
    return (
      <View className="">
        <View className="items-center justify-center mt-1 px-4">
          <Text className="font-[400] text-[26px] leading-[39px]">Catalogue</Text>
        </View>

        <View className="mt-6 px-4">
          <View className="flex-row items-center justify-center space-x-2">
            <View className="flex-row items-center ml-1 border-[1px] space-x-2 border-[#E9EAE9] flex-1">
              <View className="ml-2">
                <SearchIcon />
              </View>
              <TextInput
                placeholderTextColor={"#B3B3B3"}
                placeholder="Search Products in catalogue"
                className="flex-1"
                value={SearchVal}
                onChangeText={setSearchVal}
              />
            </View>
          </View>

          <View className="flex-row gap-[10px] mt-1 items-center justify-center">
            <View className="items-center justify-center">
              <TouchableOpacity
                style={{
                  shadowColor: "#01660D",
                  shadowOffset: {width: 0, height: 4},
                  shadowOpacity: 0.12,
                  shadowRadius: 0,
                  elevation: 1.2,
                }}
                onPress={() => {
                  router.push("/home/<USER>/product");
                }}
                className="relative items-center justify-center w-[112px] h-[68px] bg-[#F5FFF6]"
              >
                <UploadIcon />
              </TouchableOpacity>
              <View className="mt-[10px]">
                <Text>Add Product</Text>
              </View>
            </View>
            <View className="items-center justify-center">
              <TouchableOpacity
                onPress={() => {
                  router.push({
                    pathname: "/home/<USER>",
                  });
                }}
                className="relative items-center justify-center w-[112px] h-[68px] bg-[#F5FFF6]"
                style={{
                  shadowColor: "#01660D",
                  shadowOffset: {width: 0, height: 4},
                  shadowOpacity: 0.12,
                  shadowRadius: 0,
                  elevation: 1.2,
                }}
              >
                <PdfIcon />
              </TouchableOpacity>
              <View className="mt-[10px]">
                <Text>Select SKU</Text>
              </View>
            </View>
            <View className="items-center justify-center">
              <TouchableOpacity
                style={{
                  shadowColor: "#01660D",
                  shadowOffset: {width: 0, height: 4},
                  shadowOpacity: 0.12,
                  shadowRadius: 0,
                  elevation: 1.2,
                }}
                onPress={() => {
                  router.push("/home/<USER>");
                }}
                className="relative items-center justify-center w-[112px] h-[68px] bg-[#F5FFF6]"
              >
                <Addcategory />
              </TouchableOpacity>
              <View className="mt-[10px]">
                <Text>Add Options</Text>
              </View>
            </View>
          </View>

          <View className="mt-6">
            <View className="">
              <Text className="font-[400] text-[#001A03] text-[16px] leading-[24px]">
                I want to see
              </Text>
            </View>
            <View className="mt-2">
              <SlideTabs
                data={FilterTab}
                setActivefun={setActiveFilterTab}
                Active={ActiveFilterTab}
              />
            </View>
          </View>
        </View>
        <View className="py-3 flex-row bg-[#F5FFF6] mt-3 mb-3 pl-4">
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => {
              let val = !isSelectAll;
              if (val) {
                dispacher(selectAll());
              } else {
                dispacher(removeAll());
              }
            }}
            className="flex-1 items-start justify-center"
          >
            <View className="flex-row space-x-2">
              <Checkbox
                value={isSelectAll}
                onValueChange={handleSelectAll}
                color={isSelectAll ? "#B28700" : "#ccc"}
                style={{
                  borderColor: "#ccc",
                }}
              />
              <Text className="fonr-[400] text-[14px] text-[#00660A]">Product</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (qtySort === "") {
                setQtySort("DESC");
                setPriceSort("");
              } else if (qtySort === "DESC") {
                setQtySort("ASC");
                setPriceSort("");
              } else {
                setPriceSort("");
                setQtySort("");
              }
            }}
            className="flex-1 z-40 ml-[50px] items-center justify-center"
          >
            <View className="flex-row items-center">
              <Text className="fonr-[400] text-[14px] text-[#00660A]">Qty</Text>
              {/* {qtySort === "" && <Defaul width={16} height={16} />}
              {qtySort === "ASC" && <Asc width={16} height={16} />}
              {qtySort === "DESC" && <Des width={16} height={16} />} */}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (priceSort === "") {
                setPriceSort("DESC");
                setQtySort("");
              } else if (priceSort === "DESC") {
                setPriceSort("ASC");
                setQtySort("");
              } else {
                setQtySort("");
                setPriceSort("");
              }
            }}
            className="flex-1 items-center justify-center mr-[5px]"
          >
            <View className="flex-row items-center">
              <Text className="fonr-[400] text-[14px] text-[#00660A]">Price</Text>
              {/* {priceSort === "" && <Defaul width={16} height={16} />}
              {priceSort === "ASC" && <Asc width={16} height={16} />}
              {priceSort === "DESC" && <Des width={16} height={16} />} */}
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView className="bg-[#FFFFFF] min-h-[100vh]">
      <View className="flex-1">
        <Animated.FlatList
          itemLayoutAnimation={LinearTransition.duration(0)}
          showsVerticalScrollIndicator={false}
          initialNumToRender={1}
          maxToRenderPerBatch={1}
          ListEmptyComponent={() => {
            return isLoading ? null : (
              <View className="items-center justify-center flex-1">
                <Text className="">No Product in you'r shop</Text>
              </View>
            );
          }}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => {
                refresh({keyword: SearchVal});
                canShowSaveButton();
              }}
            />
          }
          ListFooterComponent={
            isLoading ? (
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  padding: 20,
                }}
              >
                <ActivityIndicator size="large" color="#00660A" />
              </View>
            ) : null
          }
          keyExtractor={({id}) => id.toString()}
          ListHeaderComponent={listHeaderComponent}
          data={productData}
          renderItem={renderItem}
        />
      </View>
    </SafeAreaView>
  );
};
export default memo(Catalogue);
